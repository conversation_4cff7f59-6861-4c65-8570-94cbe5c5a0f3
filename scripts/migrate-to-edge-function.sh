#!/bin/bash

# Migration script for HTML to PDF functionality
# From Next.js API route to Supabase Edge Function

set -e

echo "🚀 Starting migration from Next.js API route to Supabase Edge Function..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo -e "${RED}❌ Supabase CLI is not installed. Please install it first:${NC}"
    echo "npm install -g supabase"
    echo "or"
    echo "brew install supabase/tap/supabase"
    exit 1
fi

echo -e "${GREEN}✅ Supabase CLI found${NC}"

# Check if we're in a Supabase project
if [ ! -f "supabase/config.toml" ]; then
    echo -e "${RED}❌ Not in a Supabase project directory. Please run 'supabase init' first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Supabase project detected${NC}"

# Check if browserless.io token is provided
if [ -z "$BROWSERLESS_IO_TOKEN" ]; then
    echo -e "${YELLOW}⚠️  BROWSERLESS_IO_TOKEN environment variable not set.${NC}"
    echo "Please get your token from https://www.browserless.io/ and set it:"
    echo "export BROWSERLESS_IO_TOKEN=your_token_here"
    echo ""
    read -p "Enter your browserless.io token: " BROWSERLESS_IO_TOKEN
    
    if [ -z "$BROWSERLESS_IO_TOKEN" ]; then
        echo -e "${RED}❌ Token is required to proceed.${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}✅ Browserless.io token provided${NC}"

# Set the secret in Supabase
echo -e "${BLUE}📝 Setting browserless.io token in Supabase...${NC}"
supabase secrets set BROWSERLESS_IO_TOKEN="$BROWSERLESS_IO_TOKEN"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Token set successfully${NC}"
else
    echo -e "${RED}❌ Failed to set token. Please check your Supabase connection.${NC}"
    exit 1
fi

# Deploy the Edge Function
echo -e "${BLUE}🚀 Deploying html-to-pdf Edge Function...${NC}"
supabase functions deploy html-to-pdf

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Edge Function deployed successfully${NC}"
else
    echo -e "${RED}❌ Failed to deploy Edge Function.${NC}"
    exit 1
fi

# Test the function
echo -e "${BLUE}🧪 Testing the deployed function...${NC}"
TEST_HTML='<!DOCTYPE html><html><head><title>Test</title></head><body><h1>Test PDF</h1><p>This is a test document.</p></body></html>'

# Get project reference
PROJECT_REF=$(supabase status | grep "API URL" | awk '{print $3}' | sed 's/https:\/\///' | sed 's/\.supabase\.co//')

if [ -z "$PROJECT_REF" ]; then
    echo -e "${YELLOW}⚠️  Could not determine project reference. Please test manually.${NC}"
else
    echo -e "${BLUE}📋 Testing with project: $PROJECT_REF${NC}"
    
    # Note: This is a basic test - in practice you'd need the anon key
    echo -e "${YELLOW}ℹ️  To test the function, use:${NC}"
    echo "curl -X POST 'https://$PROJECT_REF.supabase.co/functions/v1/html-to-pdf' \\"
    echo "  -H 'Authorization: Bearer YOUR_ANON_KEY' \\"
    echo "  -H 'Content-Type: application/json' \\"
    echo "  -d '{\"html\":\"$TEST_HTML\",\"fileName\":\"test\"}' \\"
    echo "  --output test.pdf"
fi

echo ""
echo -e "${GREEN}🎉 Migration completed successfully!${NC}"
echo ""
echo -e "${BLUE}📋 Next steps:${NC}"
echo "1. Update your frontend code to use the new Edge Function"
echo "2. Remove the old Next.js API route: web/app/api/html-to-pdf/route.ts"
echo "3. Update any references to /api/html-to-pdf in your codebase"
echo "4. Test the new implementation thoroughly"
echo ""
echo -e "${BLUE}📚 Documentation:${NC}"
echo "- Edge Function: supabase/functions/html-to-pdf/README.md"
echo "- Frontend examples: examples/frontend-integration.ts"
echo ""
echo -e "${YELLOW}⚠️  Important:${NC}"
echo "- The old Next.js API route is still active until you remove it"
echo "- Make sure to test the new function before removing the old one"
echo "- Monitor browserless.io usage to avoid hitting limits"
echo ""
echo -e "${GREEN}✨ Happy coding!${NC}"
