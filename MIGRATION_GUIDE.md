# Migration Guide: HTML to PDF with <PERSON><PERSON> and browserless.io

This guide explains the migration from the Next.js API route using Puppeteer to a Supabase Edge Function using Playwright and browserless.io.

## Overview

### Before (Next.js API Route)
- **File**: `web/app/api/html-to-pdf/route.ts`
- **Technology**: Puppeteer + @sparticuz/chromium-min
- **Runtime**: Node.js
- **Deployment**: Vercel/Next.js serverless functions

### After (Supabase Edge Function)
- **File**: `supabase/functions/html-to-pdf/index.ts`
- **Technology**: Playwright + browserless.io
- **Runtime**: Deno
- **Deployment**: Supabase Edge Functions

## Key Benefits of Migration

| Aspect | Before | After |
|--------|--------|-------|
| **Bundle Size** | ~100MB+ (with Chromium) | ~1MB |
| **Cold Start** | 3-10 seconds | <1 second |
| **Scalability** | Limited by server resources | Auto-scaling cloud service |
| **Global Distribution** | Single region | Edge locations worldwide |
| **Maintenance** | Manual Chromium updates | Managed by browserless.io |
| **Cost** | Server resources + bandwidth | Pay-per-use + browserless.io |

## Technical Differences

### Dependencies

**Before:**
```typescript
import chromium from '@sparticuz/chromium-min';
import puppeteer from 'puppeteer';
import puppeteerCore from 'puppeteer-core';
```

**After:**
```typescript
import { chromium } from 'https://deno.land/x/playwright@1.40.0/mod.ts'
```

### Browser Connection

**Before:**
```typescript
// Complex environment detection and binary management
const isServerless = !!process.env.AWS_REGION || !!process.env.NETLIFY || !!process.env.VERCEL;

if (isLocalDevelopment) {
  const puppeteer = (await import('puppeteer')).default;
  browser = await puppeteer.launch({
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    headless: true,
  });
} else {
  const puppeteerCore = (await import('puppeteer-core')).default;
  browser = await puppeteerCore.launch({
    args: chromium.args,
    headless: 'shell',
    executablePath: await chromium.executablePath("https://github.com/Sparticuz/chromium/releases/download/v137.0.1/chromium-v137.0.1-pack.x64.tar"),
  });
}
```

**After:**
```typescript
// Simple WebSocket connection to browserless.io
const browser = await chromium.connect({
  wsEndpoint: `wss://chrome.browserless.io/playwright?token=${browserlessToken}`,
})
```

### API Differences

**Before (Puppeteer):**
```typescript
await page.setContent(html, {
  waitUntil: 'networkidle0',
});

await page.setViewport({
  width: 794,
  height: 1123,
  deviceScaleFactor: 2,
});

const pdf = await page.pdf({
  format: 'A4',
  printBackground: true,
  margin: { top: '0', right: '0', bottom: '0', left: '0' },
});
```

**After (Playwright):**
```typescript
await page.setContent(html, {
  waitUntil: 'networkidle',
});

await page.setViewportSize({
  width: 794,
  height: 1123,
});

const pdfBuffer = await page.pdf({
  format: 'A4',
  printBackground: true,
  margin: { top: '0', right: '0', bottom: '0', left: '0' },
});
```

## Migration Steps

### 1. Set up browserless.io

1. Sign up at [browserless.io](https://www.browserless.io/)
2. Get your API token
3. Set it as an environment variable in Supabase:
   ```bash
   supabase secrets set BROWSERLESS_IO_TOKEN=your_token_here
   ```

### 2. Deploy the Edge Function

```bash
# Deploy the new Edge Function
supabase functions deploy html-to-pdf
```

### 3. Update Frontend Code

**Before:**
```typescript
const response = await fetch('/api/html-to-pdf', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ html, fileName }),
});
```

**After:**
```typescript
const { data, error } = await supabase.functions.invoke('html-to-pdf', {
  body: { html, fileName },
});
```

### 4. Test and Validate

1. Test the new Edge Function with sample HTML
2. Compare PDF output quality
3. Verify performance improvements
4. Check error handling

### 5. Remove Old Implementation

Once validated, remove:
- `web/app/api/html-to-pdf/route.ts`
- Related Puppeteer dependencies from `package.json`
- Any Chromium-related configurations

## Environment Variables

### Required
- `BROWSERLESS_IO_TOKEN`: Your browserless.io API token

### Optional (Supabase automatically provides)
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_ANON_KEY`: Your Supabase anonymous key

## Error Handling

The new implementation provides better error handling:

```typescript
// Detailed error information
console.error('Error details:', {
  message: errorMessage,
  stack: errorStack,
});

return new Response(
  JSON.stringify({ 
    error: 'Failed to generate PDF',
    details: errorMessage 
  }),
  { status: 500, headers: { 'Content-Type': 'application/json' } }
);
```

## Performance Comparison

### Cold Start Times
- **Before**: 3-10 seconds (Chromium binary loading)
- **After**: <1 second (WebSocket connection)

### Memory Usage
- **Before**: 200-500MB (Chromium + Node.js)
- **After**: 10-50MB (Deno runtime only)

### Bundle Size
- **Before**: ~100MB (with Chromium binary)
- **After**: ~1MB (Playwright client only)

## Cost Considerations

### Before
- Server compute time (high due to cold starts)
- Memory usage (high due to Chromium)
- Bandwidth for large deployments

### After
- Supabase Edge Function invocations
- browserless.io API usage
- Reduced compute time due to faster execution

## Troubleshooting

### Common Issues

1. **"BROWSERLESS_IO_TOKEN not set"**
   - Ensure the environment variable is set in Supabase
   - Verify the token is valid

2. **WebSocket connection failed**
   - Check browserless.io service status
   - Verify token permissions
   - Check network connectivity

3. **PDF generation timeout**
   - Optimize HTML/CSS for faster rendering
   - Check browserless.io plan limits
   - Consider breaking large documents into smaller parts

### Debugging

```bash
# View Edge Function logs
supabase functions logs html-to-pdf

# Test locally
supabase functions serve html-to-pdf
```

## Rollback Plan

If issues arise, you can quickly rollback:

1. Keep the old Next.js API route temporarily
2. Update frontend to use the old endpoint
3. Debug the Edge Function separately
4. Re-deploy once issues are resolved

## Monitoring

Monitor the following metrics:
- Edge Function invocation count
- Error rates
- Response times
- browserless.io usage and costs

## Next Steps

After successful migration:
1. Monitor performance and costs
2. Consider implementing caching for frequently generated PDFs
3. Explore additional browserless.io features (screenshots, scraping)
4. Optimize HTML templates for better PDF generation
