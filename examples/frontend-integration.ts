// Example: How to update your frontend code to use the new Supabase Edge Function
// instead of the previous Next.js API route

import { createClient } from '@supabase/supabase-js'

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

// Updated function to use Supabase Edge Function instead of Next.js API route
export const generatePDFWithEdgeFunction = async (
  htmlContent: string, 
  fileName: string = 'document'
): Promise<Blob> => {
  try {
    // Call the Supabase Edge Function
    const { data, error } = await supabase.functions.invoke('html-to-pdf', {
      body: {
        html: htmlContent,
        fileName: fileName,
      },
    })

    if (error) {
      console.error('Edge Function error:', error)
      throw new Error(error.message || 'Failed to generate PDF')
    }

    // The response is already a Blob when using supabase.functions.invoke
    return data as Blob
  } catch (error) {
    console.error('PDF generation failed:', error)
    throw error
  }
}

// Alternative: Direct fetch approach (if you prefer not to use the Supabase client)
export const generatePDFWithFetch = async (
  htmlContent: string, 
  fileName: string = 'document'
): Promise<Blob> => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/html-to-pdf`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          html: htmlContent,
          fileName: fileName,
        }),
      }
    )

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to generate PDF')
    }

    return await response.blob()
  } catch (error) {
    console.error('PDF generation failed:', error)
    throw error
  }
}

// Example usage in a React component
export const PDFGeneratorComponent = () => {
  const [isGenerating, setIsGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleGeneratePDF = async () => {
    setIsGenerating(true)
    setError(null)

    try {
      // Your HTML content (could come from a form, editor, etc.)
      const htmlContent = `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <title>Generated PDF</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              h1 { color: #333; }
              .content { line-height: 1.6; }
            </style>
          </head>
          <body>
            <h1>Sample PDF Document</h1>
            <div class="content">
              <p>This is a sample PDF generated using Supabase Edge Functions with Playwright and browserless.io.</p>
              <p>The migration from the previous Next.js API route provides better performance and scalability.</p>
            </div>
          </body>
        </html>
      `

      // Generate PDF using the Edge Function
      const pdfBlob = await generatePDFWithEdgeFunction(htmlContent, 'sample-document')

      // Create download link
      const url = URL.createObjectURL(pdfBlob)
      const link = document.createElement('a')
      link.href = url
      link.download = 'sample-document.pdf'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div>
      <button 
        onClick={handleGeneratePDF} 
        disabled={isGenerating}
        className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
      >
        {isGenerating ? 'Generating PDF...' : 'Generate PDF'}
      </button>
      
      {error && (
        <div className="mt-2 p-2 bg-red-100 text-red-700 rounded">
          Error: {error}
        </div>
      )}
    </div>
  )
}

// Migration helper: Replace your existing API route calls
// 
// BEFORE (using Next.js API route):
// const response = await fetch('/api/html-to-pdf', { ... })
//
// AFTER (using Supabase Edge Function):
// const pdfBlob = await generatePDFWithEdgeFunction(htmlContent, fileName)

// Environment variables you'll need:
// NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
// NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
// 
// And in Supabase (via CLI or Dashboard):
// BROWSERLESS_IO_TOKEN=your_browserless_token
