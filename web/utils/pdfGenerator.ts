import { createClient } from '@/lib/supabase'

/**
 * Generate PDF from HTML content using Supabase Edge Function
 * This replaces the old Next.js API route that used Puppeteer
 */
export const generatePDFWithEdgeFunction = async (
  htmlContent: string, 
  fileName: string = 'document'
): Promise<Blob> => {
  try {
    const supabase = createClient()
    
    // Call the Supabase Edge Function
    const { data, error } = await supabase.functions.invoke('html-to-pdf', {
      body: {
        html: htmlContent,
        fileName: fileName,
      },
    })

    if (error) {
      console.error('Edge Function error:', error)
      throw new Error(error.message || 'Failed to generate PDF')
    }

    // The response is already a Blob when using supabase.functions.invoke
    return data as Blob
  } catch (error) {
    console.error('PDF generation failed:', error)
    throw error
  }
}

/**
 * Alternative: Direct fetch approach (if you prefer not to use the Supabase client)
 * This is useful if you need more control over the request or error handling
 */
export const generatePDFWithFetch = async (
  htmlContent: string, 
  fileName: string = 'document'
): Promise<Blob> => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/html-to-pdf`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          html: htmlContent,
          fileName: fileName,
        }),
      }
    )

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to generate PDF')
    }

    return await response.blob()
  } catch (error) {
    console.error('PDF generation failed:', error)
    throw error
  }
}

/**
 * Download a PDF blob as a file
 * This utility handles the download process consistently across the app
 */
export const downloadPDFBlob = (pdfBlob: Blob, fileName: string) => {
  const downloadLink = document.createElement('a')
  const url = window.URL.createObjectURL(pdfBlob)
  
  downloadLink.href = url
  downloadLink.download = `${fileName}.pdf`
  document.body.appendChild(downloadLink)
  downloadLink.click()
  
  // Cleanup
  window.URL.revokeObjectURL(url)
  document.body.removeChild(downloadLink)
}
